x-superset-image: &superset-image mlisa-data-studio
x-superset-user: &superset-user root
x-superset-depends-on: &superset-depends-on
  - db
  - redis
x-superset-volumes: &superset-volumes
  - ../../superset:/app/superset
  - ../../mlisa:/app/mlisa
  - ../../superset-frontend:/app/superset-frontend
  - superset_home:/app/superset_home

services:
  redis:
    image: redis:7
    container_name: superset_cache
    restart: unless-stopped
    ports:
      - "127.0.0.1:6379:6379"
    volumes:
      - redis:/data

  db:
    image: postgres:15
    container_name: superset_db
    restart: unless-stopped
    environment:
      POSTGRES_DB: superset
      POSTGRES_USER: superset
      POSTGRES_PASSWORD: superset
    ports:
      - "127.0.0.1:5432:5432"
    volumes:
      - db_home:/var/lib/postgresql/data

  superset:
    build:
      context: ../..
      dockerfile: mlisa/docker/Dockerfile
    env_file:
      - ./.env-mlisa
    image: *superset-image
    container_name: superset_app
    command: ["/app/scripts/docker-bootstrap-mlisa.sh", "app"]
    restart: unless-stopped
    ports:
      - 8088:8088
    user: *superset-user
    depends_on: *superset-depends-on
    volumes: *superset-volumes
    environment:
      SUPERSET_CONFIG_PATH: "/app/mlisa/config.py"
      SUPERSET_ENV: "development"

  superset-init:
    image: *superset-image
    container_name: superset_init
    command: ["/app/scripts/docker-init-mlisa.sh"]
    env_file:
      - ./.env-mlisa
    depends_on: *superset-depends-on
    user: *superset-user
    volumes: *superset-volumes
    environment:
      SUPERSET_CONFIG_PATH: "/app/mlisa/config.py"
      SUPERSET_ENV: "development"
    healthcheck:
      disable: true

  superset-worker:
    image: *superset-image
    container_name: superset_worker
    command: ["/app/scripts/docker-bootstrap-mlisa.sh", "worker"]
    env_file:
      - ./.env-mlisa
    restart: unless-stopped
    depends_on: *superset-depends-on
    user: *superset-user
    volumes: *superset-volumes
    environment:
      SUPERSET_CONFIG_PATH: "/app/mlisa/config.py"
      SUPERSET_ENV: "development"

volumes:
  superset_home:
    external: false
  db_home:
    external: false
  redis:
    external: false
