#!/bin/bash
# MLISA Data Studio Build Script

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Default values
ENVIRONMENT="dev"
BUILD_ARGS=""

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --prod|--production)
            ENVIRONMENT="prod"
            shift
            ;;
        --dev|--development)
            ENVIRONMENT="dev"
            shift
            ;;
        --no-cache)
            BUILD_ARGS="--no-cache"
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [--dev|--prod] [--no-cache]"
            echo "  --dev, --development    Build for development (default)"
            echo "  --prod, --production    Build for production"
            echo "  --no-cache             Build without using cache"
            exit 0
            ;;
        *)
            echo "Unknown option $1"
            exit 1
            ;;
    esac
done

cd "$PROJECT_ROOT"

echo "🏗️  Building MLISA Data Studio for $ENVIRONMENT environment..."

if [ "$ENVIRONMENT" = "prod" ]; then
    echo "📦 Building production image..."
    docker-compose -f mlisa/docker/docker-compose.prod.yml build $BUILD_ARGS
    echo "✅ Production build complete!"
    echo "🚀 To start: docker-compose -f mlisa/docker/docker-compose.prod.yml up -d"
else
    echo "🔧 Building development image..."
    docker-compose -f mlisa/docker/docker-compose.dev.yml build $BUILD_ARGS
    echo "✅ Development build complete!"
    echo "🚀 To start: docker-compose -f mlisa/docker/docker-compose.dev.yml up -d"
fi
