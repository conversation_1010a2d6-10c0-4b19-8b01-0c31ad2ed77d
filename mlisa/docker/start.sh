#!/bin/bash
# MLISA Data Studio Start Script

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Default values
ENVIRONMENT="dev"
ACTION="up"

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --prod|--production)
            ENVIRONMENT="prod"
            shift
            ;;
        --dev|--development)
            ENVIRONMENT="dev"
            shift
            ;;
        --stop)
            ACTION="down"
            shift
            ;;
        --restart)
            ACTION="restart"
            shift
            ;;
        --logs)
            ACTION="logs"
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [--dev|--prod] [--stop|--restart|--logs]"
            echo "  --dev, --development    Use development environment (default)"
            echo "  --prod, --production    Use production environment"
            echo "  --stop                  Stop services"
            echo "  --restart               Restart services"
            echo "  --logs                  Show logs"
            exit 0
            ;;
        *)
            echo "Unknown option $1"
            exit 1
            ;;
    esac
done

cd "$PROJECT_ROOT"

COMPOSE_FILE="mlisa/docker/docker-compose.$ENVIRONMENT.yml"

case $ACTION in
    up)
        echo "🚀 Starting MLISA Data Studio ($ENVIRONMENT)..."
        docker-compose -f "$COMPOSE_FILE" up -d
        echo "✅ Services started!"
        echo "📊 Superset: http://localhost:8088"
        echo "📝 Logs: docker-compose -f $COMPOSE_FILE logs -f"
        ;;
    down)
        echo "🛑 Stopping MLISA Data Studio ($ENVIRONMENT)..."
        docker-compose -f "$COMPOSE_FILE" down
        echo "✅ Services stopped!"
        ;;
    restart)
        echo "🔄 Restarting MLISA Data Studio ($ENVIRONMENT)..."
        docker-compose -f "$COMPOSE_FILE" restart
        echo "✅ Services restarted!"
        ;;
    logs)
        echo "📝 Showing logs for MLISA Data Studio ($ENVIRONMENT)..."
        docker-compose -f "$COMPOSE_FILE" logs -f
        ;;
esac
