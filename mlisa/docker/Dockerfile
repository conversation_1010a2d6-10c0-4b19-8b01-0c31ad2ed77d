# MLISA Data Studio - Unified Docker Image
# Builds Superset with MLISA customizations

######################################################################
# Build frontend assets
######################################################################
FROM node:18-bullseye-slim AS frontend-build

WORKDIR /app/superset-frontend

# Install dependencies
COPY superset-frontend/package*.json ./
RUN npm ci --prefer-offline --no-audit --no-fund --progress=false

# Build frontend assets
COPY superset-frontend .
RUN npm run build

######################################################################
# Main application image
######################################################################
FROM python:3.10-slim-bookworm

WORKDIR /app

# Environment variables
ENV LANG=C.UTF-8 \
    LC_ALL=C.UTF-8 \
    FLASK_APP="superset.app:create_app()" \
    SUPERSET_HOME="/app/superset_home" \
    SUPERSET_PORT=8088 \
    PYTHONPATH="/app/mlisa:/app:/app/pythonpath"

# Create directories and user
RUN mkdir -p /app/pythonpath /app/superset_home /app/scripts \
    && useradd --user-group -d ${SUPERSET_HOME} -m --no-log-init --shell /bin/bash superset

# Install system dependencies, Python deps, and clean up in single layer
RUN apt-get update -qq && apt-get install -yqq --no-install-recommends \
        build-essential \
        curl \
        default-libmysqlclient-dev \
        libsasl2-dev \
        libsasl2-modules-gssapi-mit \
        libpq-dev \
        libecpg-dev \
        libldap2-dev \
    && pip install --no-cache-dir --upgrade setuptools pip \
    && rm -rf /var/lib/apt/lists/*

# Copy Superset source and dependencies (with correct ownership from start)
COPY --chown=superset:superset superset superset
COPY --chown=superset:superset superset-frontend/package.json superset-frontend/
COPY --chown=superset:superset pyproject.toml setup.py README.md ./
COPY --chown=superset:superset requirements/base.txt requirements/

# Install Python dependencies and Superset
RUN pip install --no-cache-dir -r requirements/base.txt \
    && pip install --no-cache-dir -e .

# Copy frontend assets
COPY --chown=superset:superset --from=frontend-build /app/superset-frontend/dist superset/static/assets

# Copy MLISA customizations and install dependencies in single layer
COPY --chown=superset:superset mlisa /app/mlisa
COPY --chown=superset:superset mlisa/requirements.txt /tmp/requirements.txt
COPY --chown=superset:superset mlisa/docker/docker-bootstrap-mlisa.sh /app/scripts/
COPY --chown=superset:superset mlisa/docker/docker-init-mlisa.sh /app/scripts/

# Install MLISA package and dependencies, setup scripts, clean up in single layer
RUN cd /app/mlisa && pip install --no-cache-dir -e . \
    && if [ -s /tmp/requirements.txt ]; then pip install --no-cache-dir -r /tmp/requirements.txt; fi \
    && rm -f /tmp/requirements.txt \
    && chmod +x /app/scripts/*.sh \
    && apt-get autoremove -yqq --purge build-essential \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/* \
    && find /usr/local -name '*.pyc' -delete \
    && find /usr/local -name '__pycache__' -delete

USER superset

EXPOSE ${SUPERSET_PORT}
