# MLISA Data Studio - Unified Docker Image
# Builds Superset with MLISA customizations

######################################################################
# Build frontend assets
######################################################################
FROM node:18-bullseye-slim AS frontend-build

WORKDIR /app/superset-frontend

# Install dependencies
COPY superset-frontend/package*.json ./
RUN npm ci --prefer-offline --no-audit --no-fund --progress=false

# Build frontend assets
COPY superset-frontend .
RUN npm run build

######################################################################
# Main application image
######################################################################
FROM python:3.10-slim-bookworm

WORKDIR /app

# Environment variables
ENV LANG=C.UTF-8 \
    LC_ALL=C.UTF-8 \
    FLASK_APP="superset.app:create_app()" \
    SUPERSET_HOME="/app/superset_home" \
    SUPERSET_PORT=8088 \
    PYTHONPATH="/app/mlisa:/app:/app/pythonpath"

# Create directories and user
RUN mkdir -p /app/pythonpath /app/superset_home /app/scripts \
    && useradd --user-group -d ${SUPERSET_HOME} -m --no-log-init --shell /bin/bash superset

# Install system dependencies
RUN apt-get update -qq && apt-get install -yqq --no-install-recommends \
        build-essential \
        curl \
        default-libmysqlclient-dev \
        libsasl2-dev \
        libsasl2-modules-gssapi-mit \
        libpq-dev \
        libecpg-dev \
        libldap2-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy and install Python dependencies
COPY --chown=superset:superset pyproject.toml setup.py README.md ./
COPY --chown=superset:superset requirements/base.txt requirements/
RUN pip install --no-cache-dir --upgrade setuptools pip \
    && pip install --no-cache-dir -r requirements/base.txt

# Copy Superset source and install
COPY --chown=superset:superset superset superset
COPY --chown=superset:superset superset-frontend/package.json superset-frontend/
RUN pip install --no-cache-dir -e .

# Copy frontend assets
COPY --chown=superset:superset --from=frontend-build /app/superset-frontend/dist superset/static/assets

# Copy MLISA customizations and install
COPY --chown=superset:superset mlisa /app/mlisa
RUN cd /app/mlisa && pip install --no-cache-dir -e .

# Install MLISA extra dependencies if they exist
COPY --chown=superset:superset mlisa/requirements.txt /tmp/requirements.txt
RUN if [ -s /tmp/requirements.txt ]; then \
        pip install --no-cache-dir -r /tmp/requirements.txt; \
    fi && rm -f /tmp/requirements.txt

# Copy and setup scripts
COPY --chown=superset:superset mlisa/docker/docker-bootstrap-mlisa.sh /app/scripts/
COPY --chown=superset:superset mlisa/docker/docker-init-mlisa.sh /app/scripts/
RUN chmod +x /app/scripts/*.sh

# Clean up build dependencies
RUN apt-get autoremove -yqq --purge build-essential \
    && rm -rf /var/lib/apt/lists/*

# Set ownership
RUN chown -R superset:superset /app

USER superset

EXPOSE ${SUPERSET_PORT}
